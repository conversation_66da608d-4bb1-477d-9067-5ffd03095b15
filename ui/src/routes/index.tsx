import { useMsal } from "@azure/msal-react";
import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from "@headlessui/react";
import { useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { MultiSelect } from "primereact/multiselect";
import { useEffect, useMemo, useState } from "react";
import { FiPlus } from "react-icons/fi";
import { HiMagnifyingGlass } from "react-icons/hi2";
import { toast } from "react-toastify";

import {
  getEngagements,
  getGetClientsQueryKey,
  getGetEngagementsQueryKey,
  getGetEngagementsQueryOptions,
  getGetUsersQueryKey,
  useGetClients,
  useGetUsers,
  usePostCreateEngagement,
} from "../client.ts";
import { ActionButtons, ButtonProps } from "../components/ActionButtons.tsx";
import EngagementItem from "../components/EngagementItem.tsx";
import { EngagementUsersOptions } from "../components/EngagementUsersOptions.tsx";
import ErrorPage from "../components/ErrorHandling/ErrorPage.tsx";
import NotFound from "../components/ErrorHandling/NotFound.tsx";
import ErrorMessageModal, {
  ErrorMessage,
} from "../components/ErrorMessageModal.tsx";
import Header from "../components/Header";
import Modal from "../components/Modal.tsx";
import ResponsiveSideNav from "../components/ResponsiveSideNav.tsx";
import { useTheme } from "../context/ThemeProvider.tsx";
import {
  CreateEngagementInputBodyUsernames,
  Engagement,
  EngagementUser,
  EngagementUsers,
} from "../model";
import { errorCode } from "../utils/assets.tsx";
import { createRefetchStatus } from "../utils/refetchEngagementsStatus.ts";

export const Route = createFileRoute("/")({
  component: Index,
  loader: async ({ context: { queryClient } }) => {
    return queryClient.ensureQueryData(getGetEngagementsQueryOptions());
  },
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});

type FormData = {
  clientName: string;
  wbsCode: string;
  engagementTitle: string;
  users: EngagementUsers;
};

function Index() {
  const queryClient = useQueryClient();
  const { isDarkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const [clientQuery, setClientQuery] = useState("");
  const [isComboboxOpen, setIsComboboxOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { accounts } = useMsal();
  const currentAccount = accounts.length > 0 ? accounts[0] : null;

  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);
  const [createButtonIsDisabled, setCreateButtonIsDisabled] = useState(true);
  const engagementsQueryKey = getGetEngagementsQueryKey();
  const engagementsQueryFn = () => getEngagements();
  const { data: engagementList } = useSuspenseQuery({
    queryKey: engagementsQueryKey,
    queryFn: engagementsQueryFn,
    refetchInterval: createRefetchStatus<Engagement>("engagements", {
      refetchIntervalSeconds: 30000,
    }),
  });
  const activeEngagements = useMemo(() => {
    return (engagementList.engagements || [])
      .filter((engagement) => engagement.is_active === true)
      .sort(
        (a, b) =>
          new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime(),
      );
  }, [engagementList.engagements]);

  const filteredEngagements = useMemo(() => {
    if (searchQuery.length < 3) return activeEngagements;

    const query = searchQuery.toLowerCase();
    return activeEngagements.filter(
      (engagement: Engagement) =>
        engagement.title.toLowerCase().includes(query) ||
        engagement.client_name.toLowerCase().includes(query),
    );
  }, [activeEngagements, searchQuery]);

  const { data: availableClients } = useGetClients({
    query: {
      queryKey: getGetClientsQueryKey(),
      enabled: isOpen,
    },
  });
  const clients = availableClients?.clients || [];
  const { data: availableUsers } = useGetUsers({
    query: {
      queryKey: getGetUsersQueryKey(),
      enabled: isOpen,
    },
  });
  const users = availableUsers?.users || [];

  const [formData, setFormData] = useState<FormData>({
    clientName: "",
    wbsCode: "",
    engagementTitle: "",
    users: [],
  });

  const filteredClients =
    clientQuery === ""
      ? clients
      : clients.filter((client: string) =>
          client.toLowerCase().includes(clientQuery.toLowerCase()),
        );

  const engagementDiv =
    document.getElementById("engagements-list")?.offsetHeight;

  function closeModal() {
    setIsOpen(false);
    setFormData({
      clientName: "",
      wbsCode: "",
      engagementTitle: "",
      users: [],
    });
  }

  async function openModal() {
    setIsOpen(true);
    setFormData({
      clientName: "",
      wbsCode: "",
      engagementTitle: "",
      users: [], // We'll populate this after users are loaded
    });
  }

  useEffect(() => {
    if (isOpen && users.length > 0 && currentAccount) {
      // Filter admin users as before
      const adminUsers = users.filter(
        (u: EngagementUser) =>
          u.valid_custom_username && u.valid_ssh_key && u.app_role === "Admin",
      );

      const currentUser = users.find(
        (u: EngagementUser) => u.username === currentAccount.username,
      );

      const updatedUsers = [...adminUsers];

      if (currentUser && !updatedUsers.some((u) => u.id === currentUser.id)) {
        // Only add if user has valid username and SSH key
        if (currentUser.valid_custom_username && currentUser.valid_ssh_key) {
          updatedUsers.push(currentUser);
        }
      }

      setFormData((prev) => ({ ...prev, users: updatedUsers }));
    }
  }, [isOpen, users, currentAccount]);

  useEffect(() => {
    setCreateButtonIsDisabled(
      formData.clientName === "" ||
        formData.wbsCode === "" ||
        formData.engagementTitle === "" ||
        (formData?.users?.length ?? 0) < 1,
    );
  }, [
    formData.clientName,
    formData.users,
    formData.wbsCode,
    formData.engagementTitle,
  ]);

  const handleInputChange = (e: {
    target: { name: string; value: string };
  }) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      if (name === "users" && Array.isArray(value)) {
        // Get admin users
        const adminUsers = users.filter(
          (u: EngagementUser) =>
            u.valid_custom_username &&
            u.valid_ssh_key &&
            u.app_role === "Admin",
        );

        const currentUser = currentAccount
          ? users.find(
              (u: EngagementUser) => u.username === currentAccount.username,
            )
          : null;

        let updatedUsers = [...adminUsers];

        // Add non-admin selected users
        updatedUsers = [
          ...updatedUsers,
          ...value.filter((u: EngagementUser) => u.app_role !== "Admin"),
        ];

        // Add current user if not already included and not an admin
        if (
          currentUser &&
          currentUser.app_role !== "Admin" &&
          !updatedUsers.some((u) => u.id === currentUser.id) &&
          currentUser.valid_custom_username &&
          currentUser.valid_ssh_key
        ) {
          updatedUsers.push(currentUser);
        }

        // Remove duplicates
        updatedUsers = updatedUsers.filter(
          (user, index, self) =>
            index === self.findIndex((u) => u.id === user.id),
        );

        return { ...prev, users: updatedUsers };
      }
      return { ...prev, [name]: value };
    });
  };

  const createEngagementMutation = usePostCreateEngagement({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error creating engagement",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Engagement has been successfully created.");
        }
        const engagementsQueryKey = getGetEngagementsQueryKey();
        queryClient.invalidateQueries({ queryKey: engagementsQueryKey });
        closeModal();
      },
    },
  });

  const handleCreateEngagement = () => {
    const transformedData = {
      client_name: formData.clientName,
      engagement_title: formData.engagementTitle,
      usernames: formData.users?.map(
        (user) => user.username,
      ) as CreateEngagementInputBodyUsernames,
      wbs_code: formData.wbsCode,
    };
    createEngagementMutation.mutate({ data: transformedData });
  };

  const primaryButton: ButtonProps = {
    label: "Create",
    onClick: handleCreateEngagement,
    variant: "primary",
    disabled: createButtonIsDisabled,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      closeModal();
    },
    variant: "secondary",
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  return (
    <>
      <div className="flex min-h-screen w-full bg-slate-50 dark:bg-slate-800">
        <ResponsiveSideNav
          toggleSideNav={toggleSideNav}
          isSideNavOpen={isSideNavOpen}
        />
        <div className="flex w-full flex-col">
          <Header toggleSideNav={toggleSideNav} />
          <div className="flex-1 overflow-y-auto bg-slate-50 p-6 dark:bg-slate-800">
            <span className="pb-2 font-medium text-purple-600 dark:text-purple-300">
              Home
            </span>
            <div
              id="engagements-page-main-title"
              className="flex flex-col space-y-2"
            >
              <span className="pt-2 text-3xl font-semibold text-black dark:text-white">
                Welcome to Engage
              </span>
              <span className="pb-4 text-sm font-normal text-gray-400 md:pb-0 dark:text-white">
                A cloud service management platform
              </span>
            </div>
            <div className="mt-10 flex w-full flex-col items-center justify-between space-y-3 py-4 md:flex-row md:space-y-0 md:space-x-6 md:py-3">
              <div className="flex h-12 w-full items-center rounded-sm border border-solid border-gray-200 bg-white px-4 focus-within:ring-2 focus-within:ring-purple-500 md:mr-0 md:max-w-md dark:border-none dark:bg-slate-700">
                <HiMagnifyingGlass
                  className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
                />
                <input
                  id="search-engagements-input"
                  className="h-full w-full border-0 bg-transparent px-2 focus:outline-none dark:bg-slate-700 dark:text-white"
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  placeholder={"Search engagements or title (min 3 char.)"}
                  style={{ boxSizing: "border-box" }}
                />
              </div>

              <button
                type="button"
                onClick={openModal}
                className="flex h-14 w-full items-center justify-center space-x-3 rounded-md bg-purple-700 px-6 py-4 text-lg font-medium text-white transition-all hover:bg-purple-800 md:h-12 md:w-auto md:flex-shrink-0 md:px-6 md:py-0 md:text-base"
                style={{ boxSizing: "border-box" }}
                onMouseOver={(e) => {
                  e.currentTarget.style.cursor = "pointer";
                }}
              >
                <FiPlus className="h-7 w-7 md:h-6 md:w-6" />
                <span>Create Engagement</span>
              </button>
            </div>
            <div
              id="engagements-list"
              className={`flex flex-col space-y-3 ${engagementDiv && engagementDiv < 600 ? "h-screen" : ""}`}
            >
              {filteredEngagements?.map((engagement: Engagement) => (
                <EngagementItem
                  key={engagement.id}
                  id={engagement.id}
                  clientName={engagement.client_name}
                  title={engagement.title}
                  users={engagement.users}
                  status={engagement.status}
                  engagement={engagement}
                  // awsStatus=""  // TO BE UPDATED AFTER DECIDE WHAT TO SHOW
                  // azureStatus=""  // TO BE UPDATED - NEED TO FETCH AZURE TENANT STATUS
                  // activeProviders={["aws"]}
                />
              ))}
            </div>
          </div>
        </div>
        <Modal
          title={"Create New Engagement"}
          isOpen={isOpen}
          closeModal={closeModal}
          widthClass="w-11/12 sm:w-10/12 md:w-8/12"
        >
          <div
            id="dialog-content"
            className="space-y-4 md:space-y-6 dark:text-white"
          >
            <div className="flex flex-col space-y-4 md:flex-row md:space-y-0">
              <div className="flex w-full flex-row md:w-1/2">
                <div
                  id="client-input"
                  className="flex w-full flex-col md:w-3/4"
                >
                  <label className="mb-1 flex flex-row space-x-1 text-sm font-medium">
                    <span className="font-semibold">Client</span>
                    <span className="text-red-600">*</span>
                  </label>
                  <Combobox
                    value={formData.clientName}
                    onChange={(value: string | null) =>
                      handleInputChange({
                        target: { name: "clientName", value: value || "" },
                      })
                    }
                  >
                    <div className="relative">
                      <ComboboxInput
                        className="w-full rounded-sm border-2 border-solid border-gray-400 px-2 py-2 text-left text-gray-800 focus:text-gray-800 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
                        displayValue={(client: string) => client || ""}
                        placeholder="Select client"
                        onChange={(e) => {
                          const val = e.target.value;
                          setClientQuery(val);
                          handleInputChange({
                            target: { name: "clientName", value: val },
                          });
                          setIsComboboxOpen(true);
                        }}
                        onFocus={() => {
                          setIsComboboxOpen(true);
                        }}
                      />
                      <ComboboxButton className="absolute inset-y-0 right-0 flex items-center pr-2"></ComboboxButton>
                      {isComboboxOpen && filteredClients.length > 0 && (
                        <ComboboxOptions className="ring-opacity-5 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black dark:bg-slate-700">
                          {filteredClients.map(
                            (client: string, idx: number) => (
                              <ComboboxOption
                                key={idx}
                                value={client}
                                className={({ active }) =>
                                  `relative cursor-default py-2 pr-4 pl-10 select-none ${
                                    active
                                      ? "bg-purple-800 text-white"
                                      : "text-gray-900 dark:text-slate-100"
                                  }`
                                }
                              >
                                {({ selected, active }) => (
                                  <>
                                    <span
                                      className={`block truncate ${selected ? "font-medium" : "font-normal"}`}
                                    >
                                      {client}
                                    </span>
                                    {selected && (
                                      <span
                                        className={`absolute inset-y-0 left-0 flex items-center pl-3 ${
                                          active
                                            ? "text-white"
                                            : "text-blue-500"
                                        }`}
                                      >
                                        ✓
                                      </span>
                                    )}
                                  </>
                                )}
                              </ComboboxOption>
                            ),
                          )}
                        </ComboboxOptions>
                      )}
                    </div>
                  </Combobox>
                </div>
              </div>
              <div className="flex w-full flex-row md:w-1/2">
                <div
                  id="wbs-code-input"
                  className="flex w-full flex-col md:w-3/4"
                >
                  <label className="flex flex-row space-x-1 text-sm font-medium">
                    <span className="font-semibold">WBS code</span>
                    <span className="text-red-600">*</span>
                  </label>
                  <input
                    className="mt-1 rounded-sm border-2 border-solid border-gray-400 px-4 py-2 text-gray-400 focus:text-gray-800 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
                    placeholder="Select code"
                    value={formData?.wbsCode}
                    name="wbsCode"
                    onChange={handleInputChange}
                    type="text"
                  ></input>
                </div>
              </div>
            </div>
            <div className="flex flex-row space-x-12">
              <div id="engagement-title-input" className="flex w-full flex-col">
                <label className="flex flex-row space-x-1 text-sm font-medium">
                  <span className="font-semibold">Engagement Title</span>
                  <span className="text-red-600">*</span>
                </label>
                <input
                  className="mt-1 rounded-sm border-2 border-solid border-gray-400 px-4 py-2 text-black focus:text-gray-800 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
                  placeholder="Enter title"
                  maxLength={250}
                  name="engagementTitle"
                  value={formData.engagementTitle}
                  onChange={handleInputChange}
                  type="text"
                ></input>
                <span className="px-2 text-right text-sm font-normal text-slate-400">
                  {formData.engagementTitle.length}/250
                </span>
              </div>
            </div>
            <div className="flex flex-row space-x-12">
              <div id="add-users-input" className="flex w-full flex-col">
                <label className="flex flex-row space-x-1 text-sm font-medium">
                  <span className="font-semibold">Add Users</span>
                  <span className="text-red-600">*</span>
                </label>
                <div className="card mt-1 flex justify-center">
                  <MultiSelect
                    value={formData.users}
                    onChange={handleInputChange}
                    options={users}
                    itemTemplate={EngagementUsersOptions}
                    optionDisabled={(option: EngagementUser) =>
                      !option.valid_custom_username ||
                      !option.valid_ssh_key ||
                      option.app_role === "Admin"
                    }
                    showSelectAll={false}
                    optionLabel="full_name"
                    filter
                    placeholder="Select User"
                    display="chip"
                    name="users"
                    className="md:w-20rem w-full border-2 border-solid border-gray-400 text-gray-400"
                  />
                </div>
              </div>
            </div>
            <div className="mt-4 flex justify-end">
              <ActionButtons
                primaryButton={primaryButton}
                secondaryButton={secondaryButton}
              />
            </div>
          </div>
        </Modal>
        <ErrorMessageModal
          isOpen={isOpenErrorModal}
          closeModal={closeErrorModal}
          errorMessage={errorMessage}
        />
      </div>
    </>
  );
}
