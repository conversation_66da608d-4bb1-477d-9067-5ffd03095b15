import React from 'react';
import { HiOutlineCheckCircle, HiOutlineClock, HiOutlineExclamationCircle, HiOutlineXCircle } from 'react-icons/hi';
import { useGetUserSshKeyOperations } from '../client';
import { SSHKeyOperationStatus } from '../model';

// Helper function to determine if SSH key operation polling should continue
const createRefetchSSHKeyOperations = (options: { refetchIntervalMs?: number } = {}) => {
  const interval = options.refetchIntervalMs ?? 2000;
  return (data: unknown): number | false => {
    const operation = (data as { operation?: SSHKeyOperationStatus })?.operation;
    // Stop polling when there is no current operation
    if (!operation) return false;

    const status = String(operation.status || '').toUpperCase();
    console.log('status createRefetchSSHKeyOperations ', status)
    const transientStates = ['PENDING', 'IN_PROGRESS'];
    // Poll while transient, stop when completed/failed
    return transientStates.includes(status) ? interval : false;
  };
};

interface SSHKeyOperationsProps {
  userId: string;
}

const SSHKeyOperations: React.FC<SSHKeyOperationsProps> = ({ userId }) => {
  const { data, isLoading, error } = useGetUserSshKeyOperations(userId, {
    query: {
      refetchInterval: createRefetchSSHKeyOperations({ refetchIntervalMs: 2000 }),
      refetchIntervalInBackground: true,
    },
  });

  const operation: SSHKeyOperationStatus | null = data?.operation || null;


  // Show loader if there's an active operation in transient states
  const showLoader = !!(operation && ['PENDING', 'IN_PROGRESS'].includes(operation.status.toUpperCase()));

  // Actively polling when in transient states
  const isActivelyPolling = showLoader;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <HiOutlineCheckCircle className="h-5 w-5 text-green-500" />;
      case 'IN_PROGRESS':
        return <HiOutlineClock className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'FAILED':
        return <HiOutlineXCircle className="h-5 w-5 text-red-500" />;
      case 'PENDING':
        return <HiOutlineClock className="h-5 w-5 text-yellow-500" />;
      default:
        return <HiOutlineExclamationCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-700 bg-green-50';
      case 'IN_PROGRESS':
        return 'text-blue-700 bg-blue-50';
      case 'FAILED':
        return 'text-red-700 bg-red-50';
      case 'PENDING':
        return 'text-yellow-700 bg-yellow-50';
      default:
        return 'text-gray-700 bg-gray-50';
    }
  };

  const formatOperationType = (type: string) => {
    return type.replace('SSH_KEY_', '').replace('_', ' ').toLowerCase();
  };

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-sm text-red-500">
        Failed to load SSH key operations.
      </div>
    );
  }

  // Show loader when operation is in progress
  if (showLoader) {
    const displayStatus = operation?.status || 'PROCESSING';
    const displayMessage = operation ?
      `Started: ${new Date(operation.started_at).toLocaleString()}` :
      'Preparing SSH key operation...';

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Current SSH Key Operation</h3>
          <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
            <div className="animate-pulse w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
            <span>Live Updates Active</span>
          </div>
        </div>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 border-2 border-blue-200 dark:border-blue-700 rounded-lg shadow-sm">
            <div className="flex items-center space-x-3">
              <div className="animate-spin">
                <HiOutlineClock className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <div className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                  {operation ? formatOperationType(operation.operation_type) : 'SSH Key Operation'}
                  {operation?.ssh_key_label && (
                    <span className="ml-2 text-blue-700 dark:text-blue-300">({operation.ssh_key_label})</span>
                  )}
                </div>
                <div className="text-xs text-blue-700 dark:text-blue-300">
                  {displayMessage}
                  {operation?.completed_at && (
                    <span className="ml-2">
                      • Completed: {new Date(operation.completed_at).toLocaleString()}
                    </span>
                  )}
                </div>
                {operation?.error_message && (
                  <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                    Error: {operation.error_message}
                  </div>
                )}
              </div>
            </div>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold text-blue-800 bg-blue-200 dark:text-blue-200 dark:bg-blue-800">
              {displayStatus}
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (!operation) {
    return (
      <div className="text-sm text-gray-500 dark:text-gray-400">
        No current SSH key operations.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Current SSH Key Operation</h3>
        {isActivelyPolling && (
          <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
            <div className="animate-pulse w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
            <span>Updating...</span>
          </div>
        )}
      </div>
      <div className="space-y-3">
        <div className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-lg shadow-sm">
          <div className="flex items-center space-x-3">
            {getStatusIcon(operation.status)}
            <div>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {formatOperationType(operation.operation_type)}
                {operation.ssh_key_label && (
                  <span className="ml-2 text-gray-500 dark:text-gray-400">({operation.ssh_key_label})</span>
                )}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Started: {new Date(operation.started_at).toLocaleString()}
                {operation.completed_at && (
                  <span className="ml-2">
                    • Completed: {new Date(operation.completed_at).toLocaleString()}
                  </span>
                )}
              </div>
              {operation.error_message && (
                <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                  Error: {operation.error_message}
                </div>
              )}
            </div>
          </div>
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
              operation.status
            )}`}
          >
            {operation.status}
          </span>
        </div>
      </div>
    </div>
  );
};

export default SSHKeyOperations;
