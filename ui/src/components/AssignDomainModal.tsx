import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from "@headlessui/react";
import { useState } from "react";
import { HiChevronDown } from "react-icons/hi";

import {
  getEngagement,
  getGetEngagementsQueryKey,
  useGetEngagements,
} from "../client";
import { DomainResponse } from "../model";
import Modal from "./Modal";

interface AssignDomainModalProps {
  isOpen: boolean;
  closeModal: () => void;
  domain: DomainResponse | null;
  onAssign: (
    domain: DomainResponse,
    engagementId: string,
    nodeGroupId: string | null,
  ) => void;
}

export default function AssignDomainModal({
  isOpen,
  closeModal,
  domain,
  onAssign,
}: AssignDomainModalProps) {
  const [selectedEngagement, setSelectedEngagement] = useState<string>("");
  const [engagementQuery, setEngagementQuery] = useState("");

  // Get user's engagements
  const { data: engagementsData } = useGetEngagements({
    query: {
      queryKey: getGetEngagementsQueryKey(),
      enabled: isOpen,
    },
  });

  const engagements = engagementsData?.engagements || [];

  // Filter engagements based on search query
  const filteredEngagements =
    engagementQuery === ""
      ? engagements
      : engagements.filter((engagement) =>
          engagement.title
            .toLowerCase()
            .includes(engagementQuery.toLowerCase()),
        );

  const handleAssign = async () => {
    if (domain && selectedEngagement) {
      try {
        // Get engagement details to find the first node group
        const engagementResponse = await getEngagement(selectedEngagement);
        const engagement = engagementResponse.engagement;

        let nodeGroupId: string | null = null;
        if (engagement?.node_groups && engagement.node_groups.length > 0) {
          // Use the first node group - let backend handle the logic
          nodeGroupId = engagement.node_groups[0].id;
        }
        // If no node groups exist, pass null - backend will create a new one

        onAssign(domain, selectedEngagement, nodeGroupId);
      } catch (error) {
        console.error("Error fetching engagement details:", error);
        // TODO: Show error message to user
      }

      setSelectedEngagement("");
      setEngagementQuery("");
      closeModal();
    }
  };

  const handleClose = () => {
    setSelectedEngagement("");
    setEngagementQuery("");
    closeModal();
  };

  return (
    <Modal
      title="Assign Domain to Engagement"
      isOpen={isOpen}
      closeModal={handleClose}
      widthClass="w-11/12 sm:w-10/12 md:w-8/12 lg:w-6/12"
    >
      <div className="space-y-6">
        {domain && (
          <div className="space-y-4">
            <div>
              <label className="flex flex-row space-x-1 pb-1">
                <span className="font-semibold dark:text-white">Domain</span>
              </label>
              <div className="rounded-sm border border-gray-400 bg-gray-50 px-4 py-2 dark:border-transparent dark:bg-slate-600 dark:text-white">
                {domain.url}
              </div>
            </div>

            <div>
              <label className="flex flex-row space-x-1 pb-1">
                <span className="font-semibold dark:text-white">
                  Select Engagement
                </span>
                <span className="text-red-600">*</span>
              </label>
              <Combobox
                value={selectedEngagement}
                onChange={(newValue: string | null) => {
                  if (!newValue) return;
                  setSelectedEngagement(newValue);
                }}
              >
                <div className="relative mt-1">
                  <div className="focus-visible:ring-opacity-75 relative w-full cursor-default overflow-hidden rounded-sm border border-gray-400 bg-white text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-700 focus-visible:ring-offset-2 focus-visible:ring-offset-purple-300 sm:text-sm dark:border-transparent dark:bg-slate-600">
                    <ComboboxInput
                      className="w-full border-none py-2 pr-10 pl-3 text-sm leading-5 text-gray-900 focus:ring-0 dark:bg-slate-600 dark:text-white"
                      placeholder="Select an engagement..."
                      displayValue={(engagementId: string) => {
                        const engagement = engagements.find(
                          (e) => e.id === engagementId,
                        );
                        return engagement ? engagement.title : "";
                      }}
                      onChange={(event) => {
                        setEngagementQuery(event.target.value);
                      }}
                    />
                    <ComboboxButton className="absolute inset-y-0 right-0 flex items-center pr-2">
                      <HiChevronDown
                        className="h-5 w-5 text-gray-400"
                        aria-hidden="true"
                      />
                    </ComboboxButton>
                  </div>
                  <ComboboxOptions className="ring-opacity-5 absolute z-[9999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black focus:outline-none sm:text-sm dark:bg-slate-600">
                    {filteredEngagements.map((engagement) => (
                      <ComboboxOption
                        key={engagement.id}
                        value={engagement.id}
                        className={({ focus }) =>
                          `relative cursor-pointer py-2 pr-4 pl-10 select-none ${
                            focus
                              ? "bg-purple-600 text-white"
                              : "text-gray-900 dark:text-white"
                          }`
                        }
                      >
                        {({ selected }) => (
                          <>
                            <span
                              className={`block truncate ${selected ? "font-medium" : "font-normal"}`}
                            >
                              {engagement.title}
                            </span>
                          </>
                        )}
                      </ComboboxOption>
                    ))}

                    {filteredEngagements.length === 0 && (
                      <div
                        key="no-results"
                        className="relative cursor-default px-4 py-2 text-gray-700 select-none dark:text-gray-300"
                      >
                        No engagements found.
                      </div>
                    )}
                  </ComboboxOptions>
                </div>
              </Combobox>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <button
            onClick={handleClose}
            className="rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 focus:outline-none dark:border-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleAssign}
            disabled={!selectedEngagement}
            className="rounded-md border border-transparent bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-1 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:bg-purple-500 dark:hover:bg-purple-600"
          >
            Assign
          </button>
        </div>
      </div>
    </Modal>
  );
}
