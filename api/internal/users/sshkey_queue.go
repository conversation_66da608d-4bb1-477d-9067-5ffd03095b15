package users

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

const SSHKeyQueueName = "ssh-key-operations-queue"

type SSHKeyOperationMessage struct {
	OperationID   string    `json:"operation_id"`
	UserID        string    `json:"user_id"`
	OperationType string    `json:"operation_type"` // "SSH_KEY_DELETE", "SSH_KEY_ADD"
	SSHKey        string    `json:"ssh_key,omitempty"`
	SSHKeyLabel   string    `json:"ssh_key_label,omitempty"`
	Timestamp     time.Time `json:"timestamp"`
}

// QueueSSHKeyOperation queues an SSH key operation for background processing
func QueueSSHKeyOperation(ch *amqp.Channel, queries *db.Queries, userID, operationType, sshKey, sshKeyLabel string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// Convert operation type to ENUM
	var opTypeEnum db.SshKeyOperationTypeEnum
	switch operationType {
	case "SSH_KEY_DELETE":
		opTypeEnum = db.SshKeyOperationTypeEnumSSHKEYDELETE
	case "SSH_KEY_ADD":
		opTypeEnum = db.SshKeyOperationTypeEnumSSHKEYADD
	case "USERNAME_DELETE":
		opTypeEnum = db.SshKeyOperationTypeEnumUSERNAMEDELETE
	default:
		return fmt.Errorf("invalid operation type: %s", operationType)
	}

	// Insert operation record in database
	operationID, err := queries.InsertSSHKeyOperation(context.Background(), db.InsertSSHKeyOperationParams{
		UserID:        *userIDPgType,
		OperationType: db.NullSshKeyOperationTypeEnum{SshKeyOperationTypeEnum: opTypeEnum, Valid: true},
		SshKey:        pgtype.Text{String: sshKey, Valid: sshKey != ""},
		SshKeyLabel:   pgtype.Text{String: sshKeyLabel, Valid: sshKeyLabel != ""},
	})
	if err != nil {
		return fmt.Errorf("failed to insert SSH key operation: %w", err)
	}

	operationIDStr, err := converters.PgTypeUUIDToString(operationID)
	if err != nil {
		return fmt.Errorf("failed to convert operation ID: %w", err)
	}

	// Create message
	message := SSHKeyOperationMessage{
		OperationID:   *operationIDStr,
		UserID:        userID,
		OperationType: operationType,
		SSHKey:        sshKey,
		SSHKeyLabel:   sshKeyLabel,
		Timestamp:     time.Now(),
	}

	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal SSH key operation message: %w", err)
	}

	// Publish to queue
	err = ch.PublishWithContext(context.Background(),
		"",              // exchange
		SSHKeyQueueName, // routing key
		false,           // mandatory
		false,           // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // Make message persistent
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish SSH key operation message: %w", err)
	}

	fmt.Printf("SSH_KEY_QUEUE: Queued operation %s for user %s (operation_id: %s)\n",
		operationType, userID, *operationIDStr)
	return nil
}

// StartSSHKeyOperationWorker starts the SSH key operation worker
func StartSSHKeyOperationWorker(ch *amqp.Channel, queries *db.Queries, logger *slog.Logger, awsRootRegion, secretKey string) error {
	// Declare queue
	q, err := ch.QueueDeclare(
		SSHKeyQueueName, // name
		true,            // durable
		false,           // delete when unused
		false,           // exclusive
		false,           // no-wait
		nil,             // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare SSH key operations queue: %w", err)
	}

	// Set QoS to process one message at a time per worker
	err = ch.Qos(1, 0, false)
	if err != nil {
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack (we'll manually ack after processing)
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		return fmt.Errorf("failed to register consumer: %w", err)
	}

	logger.Info("SSH key operation worker started", "queue", q.Name)

	// Start worker goroutine
	go func() {
		for d := range msgs {
			var message SSHKeyOperationMessage
			if err := json.Unmarshal(d.Body, &message); err != nil {
				logger.Error("Failed to unmarshal SSH key operation message", "error", err)
				d.Nack(false, false) // Don't requeue malformed messages
				continue
			}

			logger.Info("Processing SSH key operation",
				"operation_id", message.OperationID,
				"user_id", message.UserID,
				"operation_type", message.OperationType)

			// Process the operation
			err := processSSHKeyOperation(queries, message, awsRootRegion, secretKey)
			if err != nil {
				logger.Error("SSH key operation failed",
					"operation_id", message.OperationID,
					"error", err)

				// Update operation status to failed
				operationIDPgType, _ := converters.StringToPgTypeUUID(message.OperationID)
				queries.UpdateSSHKeyOperationStatus(context.Background(), db.UpdateSSHKeyOperationStatusParams{
					ID:           *operationIDPgType,
					Column2:      db.SshKeyOperationStatusEnumFAILED,
					ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
					RetryCount:   pgtype.Int4{Int32: 0, Valid: true}, // Could implement retry logic here
				})

				d.Nack(false, false) // Don't requeue for now
			} else {
				logger.Info("SSH key operation completed successfully",
					"operation_id", message.OperationID)

				// Update operation status to completed
				operationIDPgType, _ := converters.StringToPgTypeUUID(message.OperationID)
				queries.UpdateSSHKeyOperationStatus(context.Background(), db.UpdateSSHKeyOperationStatusParams{
					ID:           *operationIDPgType,
					Column2:      db.SshKeyOperationStatusEnumCOMPLETED,
					ErrorMessage: pgtype.Text{Valid: false},
					RetryCount:   pgtype.Int4{Int32: 0, Valid: true},
				})

				d.Ack(false)
			}
		}
	}()

	return nil
}

// processSSHKeyOperation processes a single SSH key operation
func processSSHKeyOperation(queries *db.Queries, message SSHKeyOperationMessage, awsRootRegion, secretKey string) error {
	// Mark operation as in progress
	operationIDPgType, err := converters.StringToPgTypeUUID(message.OperationID)
	if err != nil {
		return fmt.Errorf("invalid operation ID: %w", err)
	}

	err = queries.UpdateSSHKeyOperationStatus(context.Background(), db.UpdateSSHKeyOperationStatusParams{
		ID:           *operationIDPgType,
		Column2:      db.SshKeyOperationStatusEnumINPROGRESS,
		ErrorMessage: pgtype.Text{Valid: false},
		RetryCount:   pgtype.Int4{Int32: 0, Valid: true},
	})
	if err != nil {
		return fmt.Errorf("failed to update operation status: %w", err)
	}

	// Process based on operation type
	switch message.OperationType {
	case "SSH_KEY_DELETE":
		return PropagateUserSshKeyRemovalToInstances(queries, message.UserID, awsRootRegion, secretKey)
	case "SSH_KEY_ADD":
		return PropagateUserSshKeyAdditionToInstances(queries, message.UserID, awsRootRegion, secretKey)
	default:
		return fmt.Errorf("unknown operation type: %s", message.OperationType)
	}
}
